use crate::account::AccountConfig;
use crate::types::Exchange;
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::{Arc, RwLock};

/// 配置状态
#[derive(Debug, Clone, PartialEq)]
pub enum ConfigStatus {
    /// 未配置状态
    NotConfigured,
    /// 已配置但未验证
    Configured,
    /// 已配置且验证通过
    Validated,
}

/// 数据路径配置
/// 支持为不同类型的市场数据指定不同的路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataPaths {
    /// BookTicker数据路径
    pub bookticker: Option<PathBuf>,
    /// 深度数据路径
    pub depth: Option<PathBuf>,
    /// 订单簿快照数据路径
    pub orderbook: Option<PathBuf>,
    /// 交易数据路径
    pub trades: Option<PathBuf>,
    /// 默认数据根目录（向后兼容）
    pub root: PathBuf,
}

impl Default for DataPaths {
    fn default() -> Self {
        Self {
            bookticker: None,
            depth: None,
            orderbook: None,
            trades: None,
            root: PathBuf::from("./data"),
        }
    }
}

impl DataPaths {
    /// 获取BookTicker数据路径
    /// 如果指定了专门的bookticker路径，使用该路径；否则使用root路径
    pub fn get_bookticker_path(&self) -> PathBuf {
        self.bookticker.clone().unwrap_or_else(|| self.root.clone())
    }

    /// 获取深度数据路径
    pub fn get_depth_path(&self) -> PathBuf {
        self.depth.clone().unwrap_or_else(|| self.root.clone())
    }

    /// 获取订单簿数据路径
    pub fn get_orderbook_path(&self) -> PathBuf {
        self.orderbook.clone().unwrap_or_else(|| self.root.clone())
    }

    /// 获取交易数据路径
    pub fn get_trades_path(&self) -> PathBuf {
        self.trades.clone().unwrap_or_else(|| self.root.clone())
    }

    /// 验证所有配置的路径是否存在
    pub fn validate_paths(&self) -> Result<()> {
        // 验证根路径
        if !self.root.exists() {
            return Err(BacktestError::Config(format!(
                "Root data path does not exist: {:?}",
                self.root
            )));
        }

        // 验证各个专门路径（如果配置了的话）
        if let Some(ref path) = self.bookticker {
            if !path.exists() {
                return Err(BacktestError::Config(format!(
                    "BookTicker data path does not exist: {:?}",
                    path
                )));
            }
        }

        if let Some(ref path) = self.depth {
            if !path.exists() {
                return Err(BacktestError::Config(format!(
                    "Depth data path does not exist: {:?}",
                    path
                )));
            }
        }

        if let Some(ref path) = self.orderbook {
            if !path.exists() {
                return Err(BacktestError::Config(format!(
                    "OrderBook data path does not exist: {:?}",
                    path
                )));
            }
        }

        if let Some(ref path) = self.trades {
            if !path.exists() {
                return Err(BacktestError::Config(format!(
                    "Trades data path does not exist: {:?}",
                    path
                )));
            }
        }

        Ok(())
    }
}

/// TLS证书来源类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum TlsCertSource {
    /// 使用指定的证书和私钥文件
    Files {
        cert_path: PathBuf,
        key_path: PathBuf,
    },
    /// 自动生成自签名证书（仅用于开发测试）
    SelfSigned {
        /// 证书主题名称，默认为 "localhost"
        subject: Option<String>,
    },
}

/// TLS配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsConfig {
    /// 是否启用TLS
    pub enabled: bool,

    /// 证书来源配置
    pub cert_source: Option<TlsCertSource>,
}

impl Default for TlsConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            cert_source: None,
        }
    }
}

impl TlsConfig {
    /// 创建使用文件证书的TLS配置
    pub fn with_files<P: Into<PathBuf>>(cert_path: P, key_path: P) -> Self {
        Self {
            enabled: true,
            cert_source: Some(TlsCertSource::Files {
                cert_path: cert_path.into(),
                key_path: key_path.into(),
            }),
        }
    }

    /// 创建使用自签名证书的TLS配置
    pub fn with_self_signed(subject: Option<String>) -> Self {
        Self {
            enabled: true,
            cert_source: Some(TlsCertSource::SelfSigned { subject }),
        }
    }

    /// 验证TLS配置
    pub fn validate(&self) -> Result<()> {
        if self.enabled {
            match &self.cert_source {
                Some(TlsCertSource::Files {
                    cert_path,
                    key_path,
                }) => {
                    if !cert_path.exists() {
                        return Err(BacktestError::Config(format!(
                            "TLS certificate file does not exist: {:?}",
                            cert_path
                        )));
                    }
                    if !key_path.exists() {
                        return Err(BacktestError::Config(format!(
                            "TLS private key file does not exist: {:?}",
                            key_path
                        )));
                    }
                }
                Some(TlsCertSource::SelfSigned { .. }) => {
                    // 自签名证书无需验证文件存在性，会在运行时生成
                }
                None => {
                    return Err(BacktestError::Config(
                        "TLS certificate source is required when TLS is enabled".to_string(),
                    ));
                }
            }
        }
        Ok(())
    }
}

/// 全局配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 交易所名称
    pub exchange: Exchange,

    /// 回测开始时间
    pub start_time: DateTime<Utc>,

    /// 回测结束时间
    pub end_time: DateTime<Utc>,

    /// 数据路径配置（支持不同数据类型的路径）
    pub data_paths: DataPaths,

    /// WebSocket服务器端口
    pub websocket_port: u16,

    /// HTTP服务器端口
    pub http_port: u16,

    /// 日志级别
    pub log_level: String,

    /// 性能预期（微秒）
    pub performance_target_us: u64,

    /// HTTP服务器TLS配置
    pub http_tls: TlsConfig,

    /// WebSocket服务器TLS配置
    pub websocket_tls: TlsConfig,

    /// 账户配置
    pub account: AccountConfig,
}

/// 配置状态管理结构
#[derive(Debug, Clone)]
pub struct ConfigState {
    pub config: Config,
    pub status: ConfigStatus,
    pub is_user_configured: bool, // 标记是否由用户主动配置
}

impl Default for Config {
    fn default() -> Self {
        // 设置一个较大的时间范围以包含测试数据
        let start_time = DateTime::from_timestamp(**********, 0).unwrap_or_else(|| Utc::now()); // 2025-07-07 11:23:20 UTC
        let end_time = DateTime::from_timestamp(**********, 0).unwrap_or_else(|| Utc::now()); // 2025-07-07 11:25:00 UTC

        Self {
            exchange: Exchange::Binance,
            start_time,
            end_time,
            data_paths: DataPaths::default(),
            websocket_port: 8080,
            http_port: 8081,
            log_level: "info".to_string(),
            performance_target_us: 500,
            http_tls: TlsConfig::default(),
            websocket_tls: TlsConfig::default(),
            account: AccountConfig::default(),
        }
    }
}

impl Default for ConfigState {
    fn default() -> Self {
        Self {
            config: Config::default(),
            status: ConfigStatus::NotConfigured,
            is_user_configured: false,
        }
    }
}

/// 线程安全的全局配置状态单例
static GLOBAL_CONFIG_STATE: Lazy<Arc<RwLock<ConfigState>>> =
    Lazy::new(|| Arc::new(RwLock::new(ConfigState::default())));

/// 配置管理器
pub struct ConfigManager;

impl ConfigManager {
    /// 获取配置的只读引用
    pub fn get() -> Result<Config> {
        GLOBAL_CONFIG_STATE
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config: {}", e)))
            .map(|state| state.config.clone())
    }

    /// 获取配置状态
    pub fn get_status() -> Result<ConfigStatus> {
        GLOBAL_CONFIG_STATE
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config status: {}", e)))
            .map(|state| state.status.clone())
    }

    /// 检查是否已配置
    pub fn is_configured() -> Result<bool> {
        let status = Self::get_status()?;
        Ok(status != ConfigStatus::NotConfigured)
    }

    /// 检查是否由用户配置
    pub fn is_user_configured() -> Result<bool> {
        GLOBAL_CONFIG_STATE
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config: {}", e)))
            .map(|state| state.is_user_configured)
    }

    /// 检查配置是否可以启动系统
    pub fn can_start() -> Result<bool> {
        let state = GLOBAL_CONFIG_STATE
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config: {}", e)))?;

        Ok(state.is_user_configured && state.status == ConfigStatus::Validated)
    }

    /// 更新配置
    pub fn update<F>(updater: F) -> Result<()>
    where
        F: FnOnce(&mut Config),
    {
        let mut state = GLOBAL_CONFIG_STATE
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        updater(&mut state.config);
        state.status = ConfigStatus::Configured;
        state.is_user_configured = true;
        Ok(())
    }

    /// 从文件加载配置（支持JSON和TOML格式）
    pub fn load_from_file(path: &PathBuf) -> Result<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| BacktestError::Config(format!("Failed to read config file: {}", e)))?;

        let config: Config = if path.extension().and_then(|s| s.to_str()) == Some("toml") {
            toml::from_str(&content)
                .map_err(|e| BacktestError::Config(format!("Failed to parse TOML config: {}", e)))?
        } else {
            serde_json::from_str(&content)
                .map_err(|e| BacktestError::Config(format!("Failed to parse JSON config: {}", e)))?
        };

        Self::set(config)
    }

    /// 保存配置到文件（支持JSON和TOML格式）
    pub fn save_to_file(path: &PathBuf) -> Result<()> {
        let config = Self::get()?;

        let content = if path.extension().and_then(|s| s.to_str()) == Some("toml") {
            toml::to_string_pretty(&config).map_err(|e| {
                BacktestError::Config(format!("Failed to serialize TOML config: {}", e))
            })?
        } else {
            serde_json::to_string_pretty(&config).map_err(|e| {
                BacktestError::Config(format!("Failed to serialize JSON config: {}", e))
            })?
        };

        std::fs::write(path, content)
            .map_err(|e| BacktestError::Config(format!("Failed to write config file: {}", e)))?;

        Ok(())
    }

    /// 设置整个配置
    pub fn set(config: Config) -> Result<()> {
        let mut state = GLOBAL_CONFIG_STATE
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        state.config = config;
        state.status = ConfigStatus::Configured;
        state.is_user_configured = true;
        Ok(())
    }

    /// 设置配置并标记为用户配置
    pub fn set_user_config(config: Config) -> Result<()> {
        let mut state = GLOBAL_CONFIG_STATE
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        state.config = config;
        state.status = ConfigStatus::Configured;
        state.is_user_configured = true;
        Ok(())
    }

    /// 重置配置为默认状态
    pub fn reset() -> Result<()> {
        let mut state = GLOBAL_CONFIG_STATE
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        *state = ConfigState::default();
        Ok(())
    }

    /// 验证配置的有效性
    pub fn validate() -> Result<()> {
        let mut state = GLOBAL_CONFIG_STATE
            .write()
            .map_err(|e| BacktestError::Config(format!("Failed to write config: {}", e)))?;

        let config = &state.config;

        if config.start_time >= config.end_time {
            return Err(BacktestError::Config(
                "Start time must be before end time".to_string(),
            ));
        }

        // 验证数据路径
        config.data_paths.validate_paths()?;

        if config.websocket_port == config.http_port {
            return Err(BacktestError::Config(
                "WebSocket and HTTP ports must be different".to_string(),
            ));
        }

        // 验证TLS配置
        config.http_tls.validate()?;
        config.websocket_tls.validate()?;

        // 验证通过，更新状态
        state.status = ConfigStatus::Validated;
        Ok(())
    }

    /// 强制验证配置（用于启动前检查）
    pub fn ensure_can_start() -> Result<()> {
        let state = GLOBAL_CONFIG_STATE
            .read()
            .map_err(|e| BacktestError::Config(format!("Failed to read config: {}", e)))?;

        if !state.is_user_configured {
            return Err(BacktestError::Config(
                "Configuration not set. Please configure the system before starting.".to_string(),
            ));
        }

        if state.status != ConfigStatus::Validated {
            drop(state); // 释放读锁
            Self::validate()?; // 尝试验证
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_manager() {
        // 重置配置状态
        ConfigManager::reset().unwrap();

        // 测试默认配置状态
        assert!(!ConfigManager::is_user_configured().unwrap());
        assert!(!ConfigManager::can_start().unwrap());
        assert_eq!(
            ConfigManager::get_status().unwrap(),
            ConfigStatus::NotConfigured
        );

        // 测试默认配置
        let config = ConfigManager::get().unwrap();
        assert_eq!(config.websocket_port, 8080);

        // 测试更新配置
        ConfigManager::update(|config| {
            config.websocket_port = 9090;
        })
        .unwrap();

        let updated_config = ConfigManager::get().unwrap();
        assert_eq!(updated_config.websocket_port, 9090);

        // 验证配置状态已更新
        assert!(ConfigManager::is_user_configured().unwrap());
        assert_eq!(
            ConfigManager::get_status().unwrap(),
            ConfigStatus::Configured
        );

        // 验证配置
        ConfigManager::validate().unwrap();
        assert_eq!(
            ConfigManager::get_status().unwrap(),
            ConfigStatus::Validated
        );
        assert!(ConfigManager::can_start().unwrap());
    }

    #[test]
    fn test_config_validation() {
        // 重置配置状态
        ConfigManager::reset().unwrap();

        // 测试未配置时不能启动
        assert!(ConfigManager::ensure_can_start().is_err());

        // 设置配置
        ConfigManager::set_user_config(Config::default()).unwrap();

        // 验证配置
        assert!(ConfigManager::ensure_can_start().is_ok());
    }
}
